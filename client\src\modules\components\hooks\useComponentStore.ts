/**
 * Component Management Store
 * Zustand store for managing component UI state
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { 
  ComponentFilters, 
  ComponentListState, 
  ComponentSearchState,
  BulkOperationState,
  ComponentDisplayOptions 
} from '../types';

interface ComponentStore {
  // List state
  listState: ComponentListState;
  setListState: (state: Partial<ComponentListState>) => void;
  updateFilters: (filters: Partial<ComponentFilters>) => void;
  clearFilters: () => void;
  
  // Search state
  searchState: ComponentSearchState;
  setSearchState: (state: Partial<ComponentSearchState>) => void;
  addToSearchHistory: (query: string) => void;
  clearSearchHistory: () => void;
  
  // Bulk operations
  bulkState: BulkOperationState;
  setBulkState: (state: Partial<BulkOperationState>) => void;
  selectComponent: (id: number) => void;
  deselectComponent: (id: number) => void;
  selectAll: (ids: number[]) => void;
  clearSelection: () => void;
  
  // Display options
  displayOptions: ComponentDisplayOptions;
  setDisplayOptions: (options: Partial<ComponentDisplayOptions>) => void;
  
  // UI state
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  
  // Reset all state
  reset: () => void;
}

const defaultFilters: ComponentFilters = {
  search_term: '',
  category: null,
  component_type: null,
  manufacturer: '',
  is_preferred: null,
  is_active: null,
  min_price: null,
  max_price: null,
  currency: 'USD',
  stock_status: '',
};

const defaultListState: ComponentListState = {
  filters: defaultFilters,
  sortBy: 'name',
  sortOrder: 'asc',
  page: 1,
  pageSize: 20,
  viewMode: 'grid',
  selectedComponents: [],
};

const defaultSearchState: ComponentSearchState = {
  isAdvancedMode: false,
  searchHistory: [],
  recentSearches: [],
  savedSearches: [],
};

const defaultBulkState: BulkOperationState = {
  selectedIds: [],
  operation: null,
  isProcessing: false,
  progress: 0,
  results: [],
};

const defaultDisplayOptions: ComponentDisplayOptions = {
  showImages: true,
  showSpecifications: true,
  showPricing: true,
  showAvailability: true,
  compactMode: false,
};

export const useComponentStore = create<ComponentStore>()(
  devtools(
    persist(
      (set, get) => ({
        // List state
        listState: defaultListState,
        setListState: (state) =>
          set((prev) => ({
            listState: { ...prev.listState, ...state },
          })),
        
        updateFilters: (filters) =>
          set((prev) => ({
            listState: {
              ...prev.listState,
              filters: { ...prev.listState.filters, ...filters },
              page: 1, // Reset to first page when filters change
            },
          })),
        
        clearFilters: () =>
          set((prev) => ({
            listState: {
              ...prev.listState,
              filters: defaultFilters,
              page: 1,
            },
          })),

        // Search state
        searchState: defaultSearchState,
        setSearchState: (state) =>
          set((prev) => ({
            searchState: { ...prev.searchState, ...state },
          })),
        
        addToSearchHistory: (query) =>
          set((prev) => {
            const history = prev.searchState.searchHistory.filter(h => h !== query);
            return {
              searchState: {
                ...prev.searchState,
                searchHistory: [query, ...history].slice(0, 10), // Keep last 10
              },
            };
          }),
        
        clearSearchHistory: () =>
          set((prev) => ({
            searchState: {
              ...prev.searchState,
              searchHistory: [],
            },
          })),

        // Bulk operations
        bulkState: defaultBulkState,
        setBulkState: (state) =>
          set((prev) => ({
            bulkState: { ...prev.bulkState, ...state },
          })),
        
        selectComponent: (id) =>
          set((prev) => {
            const selectedIds = prev.bulkState.selectedIds.includes(id)
              ? prev.bulkState.selectedIds
              : [...prev.bulkState.selectedIds, id];
            
            return {
              bulkState: { ...prev.bulkState, selectedIds },
              listState: { ...prev.listState, selectedComponents: selectedIds },
            };
          }),
        
        deselectComponent: (id) =>
          set((prev) => {
            const selectedIds = prev.bulkState.selectedIds.filter(selectedId => selectedId !== id);
            
            return {
              bulkState: { ...prev.bulkState, selectedIds },
              listState: { ...prev.listState, selectedComponents: selectedIds },
            };
          }),
        
        selectAll: (ids) =>
          set((prev) => ({
            bulkState: { ...prev.bulkState, selectedIds: ids },
            listState: { ...prev.listState, selectedComponents: ids },
          })),
        
        clearSelection: () =>
          set((prev) => ({
            bulkState: { ...prev.bulkState, selectedIds: [] },
            listState: { ...prev.listState, selectedComponents: [] },
          })),

        // Display options
        displayOptions: defaultDisplayOptions,
        setDisplayOptions: (options) =>
          set((prev) => ({
            displayOptions: { ...prev.displayOptions, ...options },
          })),

        // UI state
        sidebarOpen: true,
        setSidebarOpen: (open) => set({ sidebarOpen: open }),

        // Reset
        reset: () =>
          set({
            listState: defaultListState,
            searchState: defaultSearchState,
            bulkState: defaultBulkState,
            displayOptions: defaultDisplayOptions,
            sidebarOpen: true,
          }),
      }),
      {
        name: 'component-store',
        partialize: (state) => ({
          listState: {
            filters: state.listState.filters,
            sortBy: state.listState.sortBy,
            sortOrder: state.listState.sortOrder,
            pageSize: state.listState.pageSize,
            viewMode: state.listState.viewMode,
          },
          searchState: {
            searchHistory: state.searchState.searchHistory,
            savedSearches: state.searchState.savedSearches,
          },
          displayOptions: state.displayOptions,
          sidebarOpen: state.sidebarOpen,
        }),
      }
    ),
    { name: 'component-store' }
  )
);

// Selector hooks for better performance
export const useComponentFilters = () => useComponentStore((state) => state.listState.filters);
export const useComponentViewMode = () => useComponentStore((state) => state.listState.viewMode);
export const useComponentSelection = () => useComponentStore((state) => state.bulkState.selectedIds);
export const useComponentDisplayOptions = () => useComponentStore((state) => state.displayOptions);
